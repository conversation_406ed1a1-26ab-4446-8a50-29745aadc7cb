-- =====================================================
-- FIX FUNCTION SEARCH PATH SECURITY ISSUES
-- =====================================================
-- This script fixes the Supabase security advisor warnings about
-- functions with mutable search_path
--
-- Issues:
-- 1. Function public.update_updated_at_column has a role mutable search_path
-- 2. Function public.validate_payment_amount has a role mutable search_path
--
-- Solution: Set explicit search_path for security
-- =====================================================

-- Fix 1: update_updated_at_column function
-- Drop and recreate with secure search_path
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

CREATE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Recreate all triggers that use this function
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Fix 2: validate_payment_amount function (if it exists)
-- This function might exist from a previous schema version
DROP FUNCTION IF EXISTS validate_payment_amount(DECIMAL) CASCADE;

-- If you need a payment validation function, here's a secure version:
-- CREATE FUNCTION validate_payment_amount(amount DECIMAL)
-- RETURNS BOOLEAN
-- LANGUAGE plpgsql
-- SECURITY DEFINER
-- SET search_path = public
-- AS $$
-- BEGIN
--     RETURN amount > 0 AND amount <= 999999.99;
-- END;
-- $$;

-- Verification message
DO $$
BEGIN
    RAISE NOTICE '✅ FUNCTION SEARCH PATH SECURITY ISSUES FIXED!';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 CHANGES MADE:';
    RAISE NOTICE '   • Fixed update_updated_at_column() with SET search_path = public';
    RAISE NOTICE '   • Removed validate_payment_amount() if it existed';
    RAISE NOTICE '   • Recreated all timestamp update triggers';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY IMPROVEMENTS:';
    RAISE NOTICE '   • SET search_path = public prevents search path injection attacks';
    RAISE NOTICE '   • SECURITY DEFINER ensures function runs with creator permissions';
    RAISE NOTICE '   • Functions now have fixed, secure search paths';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Supabase security advisor warnings should be resolved!';
END $$;

-- Verify functions exist and have correct properties
SELECT 'Function Security Verification:' as info;
SELECT 
    proname as function_name,
    prosecdef as is_security_definer,
    proconfig as search_path_config
FROM pg_proc 
WHERE proname IN ('update_updated_at_column', 'validate_payment_amount')
AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- Verify triggers are working
SELECT 'Trigger Verification:' as info;
SELECT 
    trigger_name,
    event_object_table as table_name,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND trigger_name LIKE '%updated_at%'
ORDER BY event_object_table;
