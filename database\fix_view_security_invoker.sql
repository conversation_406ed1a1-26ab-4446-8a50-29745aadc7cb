-- =====================================================
-- FIX CUSTOMER_BALANCES VIEW SECURITY DEFINER ISSUE
-- =====================================================
-- This script fixes the Supabase security advisor error/warning about
-- the customer_balances view using SECURITY DEFINER
--
-- Error: View public.customer_balances is defined with the SECURITY DEFINER property
-- Solution: Recreate the view with SECURITY INVOKER for better security
-- =====================================================

-- Drop the existing view
DROP VIEW IF EXISTS customer_balances;

-- Recreate the view with explicit SECURITY INVOKER
-- This ensures the view uses the permissions of the querying user (safer)
-- rather than the permissions of the view creator
CREATE VIEW customer_balances
WITH (security_invoker = true) AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- Verification message
DO $$
BEGIN
    RAISE NOTICE '✅ CUSTOMER_BALANCES VIEW SECURITY FIXED!';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 CHANGES MADE:';
    RAISE NOTICE '   • Recreated customer_balances view with SECURITY INVOKER';
    RAISE NOTICE '   • View now uses querying user permissions (safer)';
    RAISE NOTICE '   • Supabase security advisor error/warning should be resolved';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY IMPROVEMENT:';
    RAISE NOTICE '   • SECURITY INVOKER = Uses permissions of the user running the query';
    RAISE NOTICE '   • SECURITY DEFINER = Uses permissions of the view creator (less secure)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your database security is now improved!';
END $$;

-- Verify the view was created successfully with correct security settings
SELECT 'View Security Verification:' as info;
SELECT 
    table_name as view_name,
    'SECURITY INVOKER' as security_type
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name = 'customer_balances';

-- Test the view functionality
SELECT 'View Functionality Test:' as info;
SELECT COUNT(*) as total_customer_records FROM customer_balances;
