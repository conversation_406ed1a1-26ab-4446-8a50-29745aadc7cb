-- =====================================================
-- FIX RLS POLICIES FOR CUSTOM AUTHENTICATION
-- =====================================================
-- This script fixes the Row Level Security policies to work with custom authentication
-- Run this in Supabase SQL Editor to fix the "Database operation failed" error
--
-- Problem: The app uses custom authentication (localStorage) but RLS policies 
-- were checking for Supabase auth.role() = 'authenticated'
-- Solution: Update policies to allow operations from the application
-- =====================================================

-- Drop existing policies that check for Supabase authentication
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_payments;

-- Create new policies that allow all operations for the application
-- These work with custom authentication systems
CREATE POLICY "Enable all operations for application" ON products
FOR ALL USING (true);

CREATE POLICY "Enable all operations for application" ON customers
FOR ALL USING (true);

CREATE POLICY "Enable all operations for application" ON customer_debts
FOR ALL USING (true);

CREATE POLICY "Enable all operations for application" ON customer_payments
FOR ALL USING (true);

-- Verification message
DO $$
BEGIN
    RAISE NOTICE '✅ RLS POLICIES FIXED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 CHANGES MADE:';
    RAISE NOTICE '   • Removed Supabase auth-dependent policies';
    RAISE NOTICE '   • Added application-friendly policies';
    RAISE NOTICE '   • All database operations should now work';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RESULT:';
    RAISE NOTICE '   • "Database operation failed" error should be resolved';
    RAISE NOTICE '   • Debt records can now be saved successfully';
    RAISE NOTICE '   • All CRUD operations should work normally';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your Tindahan store is ready to use!';
END $$;

-- Show current policies for verification
SELECT 'Current RLS Policies:' as info;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('products', 'customers', 'customer_debts', 'customer_payments')
ORDER BY tablename, policyname;
