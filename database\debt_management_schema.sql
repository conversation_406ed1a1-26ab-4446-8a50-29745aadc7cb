-- =====================================================
-- DEBT MANAGEMENT SYSTEM - COMPREHENSIVE DATABASE SCHEMA
-- =====================================================
-- Professional debt management system for Tindahan store
-- This file contains all necessary tables, views, functions, and triggers for debt management
-- Can be directly pasted into Supabase SQL Editor for complete setup
--
-- Features:
-- ✅ Customer Debt Management with product tracking
-- ✅ Payment Recording with family member responsibility
-- ✅ Automatic balance calculations
-- ✅ Comprehensive indexing for optimal performance
-- ✅ Automatic timestamp management
-- ✅ Data integrity constraints
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CUSTOMER DEBTS TABLE
-- =====================================================
-- Manages customer debt records with product details
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMER PAYMENTS TABLE
-- =====================================================
-- Manages payment records with family member tracking
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    responsible_family_member VARCHAR(255), -- Optional: Name of family member who made payment
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMER BALANCE VIEW
-- =====================================================
-- Real-time view for customer debt balances
CREATE OR REPLACE VIEW customer_balances AS
SELECT 
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count
FROM (
    SELECT 
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts 
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT 
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments 
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================
-- Optimized indexes for fast queries and better performance

-- Customer debt indexes
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_product ON customer_debts(product_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);
CREATE INDEX IF NOT EXISTS idx_customer_debts_amount ON customer_debts(total_amount);

-- Customer payment indexes
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_customer_payments_amount ON customer_payments(payment_amount);
CREATE INDEX IF NOT EXISTS idx_customer_payments_method ON customer_payments(payment_method);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================
-- Automatic timestamp management and business logic

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================
-- Comprehensive sample data to test all functionality immediately

-- Insert sample debt records (with safe duplicate handling)
INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Juan', 'Dela Cruz', 'Lucky Me Pancit Canton', 15.00, 3, '2024-01-15', 'Regular customer'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts 
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz' 
    AND product_name = 'Lucky Me Pancit Canton' AND debt_date = '2024-01-15'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Maria', 'Santos', 'Coca-Cola', 25.00, 2, '2024-01-16', 'Neighbor customer'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts 
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos' 
    AND product_name = 'Coca-Cola' AND debt_date = '2024-01-16'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Pedro', 'Garcia', 'Rice', 55.00, 1, '2024-01-17', 'Weekly rice purchase'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts 
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia' 
    AND product_name = 'Rice' AND debt_date = '2024-01-17'
);

-- Insert sample payment records
INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, responsible_family_member, notes)
SELECT 'Juan', 'Dela Cruz', 30.00, '2024-01-20', 'Ana Dela Cruz', 'Partial payment by daughter'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments 
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz' 
    AND payment_date = '2024-01-20' AND payment_amount = 30.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, notes)
SELECT 'Maria', 'Santos', 50.00, '2024-01-18', 'Full payment for recent purchases'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments 
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos' 
    AND payment_date = '2024-01-18' AND payment_amount = 50.00
);

-- =====================================================
-- SECURITY POLICIES (RLS)
-- =====================================================
-- Enable Row Level Security for data protection

-- Enable RLS on tables
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON customer_debts
FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON customer_payments
FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
-- Success message for database setup completion

DO $$
BEGIN
    RAISE NOTICE '✅ Debt Management System Database Setup Complete!';
    RAISE NOTICE '📊 Tables created: customer_debts, customer_payments';
    RAISE NOTICE '👁️ Views created: customer_balances';
    RAISE NOTICE '🔍 Indexes created for optimal performance';
    RAISE NOTICE '🔒 Security policies enabled';
    RAISE NOTICE '📝 Sample data inserted for testing';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Ready to use! You can now:';
    RAISE NOTICE '   • Add customer debt records';
    RAISE NOTICE '   • Record payments';
    RAISE NOTICE '   • Track family member responsibilities';
    RAISE NOTICE '   • View real-time balances';
END $$;
