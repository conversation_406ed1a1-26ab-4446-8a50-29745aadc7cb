import { createClient } from '@supabase/supabase-js'

import { config } from './env'

// Create Supabase client with validated environment variables
export const supabase = createClient(
  config.database.url,
  config.database.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    db: {
      schema: 'public'
    },
    global: {
      headers: {
        'X-Client-Info': 'revantad-store@1.0.0'
      }
    }
  }
)

// Database Types
export interface Product {
  id: string
  name: string
  image_url?: string
  net_weight: string
  price: number
  stock_quantity: number
  category: string
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  customer_name: string
  customer_family_name: string
  profile_picture_url?: string
  profile_picture_public_id?: string
  phone_number?: string
  address?: string
  notes?: string
  created_at: string
  updated_at: string
}

// Customer Debt Management Interfaces
export interface CustomerDebt {
  id: string
  customer_name: string
  customer_family_name: string
  product_name: string
  product_price: number
  quantity: number
  total_amount: number
  debt_date: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerPayment {
  id: string
  customer_name: string
  customer_family_name: string
  payment_amount: number
  payment_date: string
  payment_method: string
  responsible_family_member?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerBalance {
  customer_name: string
  customer_family_name: string
  total_debt: number
  total_payments: number
  remaining_balance: number
  last_debt_date?: string
  last_payment_date?: string
  debt_count: number
  payment_count: number
}

// Payment Methods
export const PAYMENT_METHODS = [
  'Cash',
  'GCash',
  'PayMaya',
  'Bank Transfer',
  'Others'
] as const

export type PaymentMethod = typeof PAYMENT_METHODS[number]



// Product Categories
export const PRODUCT_CATEGORIES = [
  'Snacks',
  'Canned Goods',
  'Beverages',
  'Personal Care',
  'Household Items',
  'Condiments',
  'Rice & Grains',
  'Instant Foods',
  'Dairy Products',
  'Others'
] as const

export type ProductCategory = typeof PRODUCT_CATEGORIES[number]
